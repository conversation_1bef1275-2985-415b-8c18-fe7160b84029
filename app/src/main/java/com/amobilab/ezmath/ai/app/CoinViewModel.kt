package com.amobilab.ezmath.ai.app

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryEntity
import com.amobilab.ezmath.ai.data.db.powerSync.TransactionType
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

@Singleton
class CoinViewModel @Inject constructor() : ViewModel() {

    var tooltipDisplayCountScanTab = 0

    val maxTooltipDisplaysScanTab = 2

    var tooltipDisplayCountChatCompose = 0

    val maxTooltipDisplaysChatCompose = 2

    init {
        viewModelScope.launch {
            try {
                AppDatabase.Companion.getInstance().connectToBackend()
                debugLog("Connected to PowerSync")
            } catch (e: Exception) {
                debugLog("Connection error: ${e.message}")
            }
        }
    }

    private val _isShowCoinInsufficientDialog = MutableStateFlow(false)
    val isShowCoinInsufficientDialog = _isShowCoinInsufficientDialog.asStateFlow()
    
    fun showInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = true
    }
    fun hideInsufficientCoinDialog() {
        _isShowCoinInsufficientDialog.value = false
    }

    val appThemeMode: MutableLiveData<AppThemeMode> by lazy {
        val savedAppThemeMode = PrefAssist.getString(
            PrefConst.APP_THEME_MODE,
            AppThemeMode.SYSTEM.name
        )
        MutableLiveData<AppThemeMode>(AppThemeMode.valueOf(savedAppThemeMode))
    }


    fun setAppTheme(appThemeMode: AppThemeMode) {
        this.appThemeMode.value = appThemeMode
        PrefAssist.setString(PrefConst.APP_THEME_MODE, appThemeMode.name)
    }


    private val _coinTotal = MutableStateFlow(PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0))
    val coinTotal = _coinTotal.asStateFlow()

    fun setCoinBalance(newCoinTotal: Long) {
        _coinTotal.value = newCoinTotal
        PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, newCoinTotal)

        debugLog("saveCoinTotal: $newCoinTotal")
    }

    fun updateCoinBalance(amount: Long, description: String) {
        val coinTotal = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
        viewModelScope.launch(Dispatchers.IO) {
            if (FirestoreUtils.syncCoinsInterval % 10 != 0L) {
                FirestoreUtils.syncCoinsFirestore()
            }
            FirestoreUtils.syncCoinsInterval++

            withContext(Dispatchers.Main) {
                setCoinBalance(coinTotal + amount)
            }

            AppDatabase
                .getInstance()
                .getCoinHistoryRepository()
                .insertTransaction(
                    CoinHistoryEntity(
                        type = if (amount > 0) TransactionType.EARN else TransactionType.SPEND,
                        amount = abs(amount.toInt()),
                        date = System.currentTimeMillis(),
                        description = description
                    )
                )
        }
    }


    private val _freeChat = MutableStateFlow(PrefAssist.getInt(PrefConst.FREE_CHAT))
    val freeChat = _freeChat.asStateFlow()

    fun countdownFreeChat() {
        val newFreeChat = PrefAssist.getInt(PrefConst.FREE_CHAT) - 1
        _freeChat.value = newFreeChat
    }

}