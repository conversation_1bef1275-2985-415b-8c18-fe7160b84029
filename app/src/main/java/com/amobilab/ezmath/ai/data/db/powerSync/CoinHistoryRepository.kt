package com.amobilab.ezmath.ai.data.db.powerSync

import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.Queries
import com.powersync.db.SqlCursor
import com.powersync.db.getLongOptional
import com.powersync.db.getStringOptional
import com.powersync.db.internal.PowerSyncTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.UUID

data class CoinHistoryEntity(
    val id: String = "",
    val type: TransactionType,
    val amount: Int,
    val date: Long,
    val description: String
)

enum class TransactionType {
    EARN, SPEND
}

class CoinHistoryRepository(private val database: PowerSyncDatabase) {

    // Mapper sử dụng các phương thức tiện ích của SqlCursor
    private val coinHistoryEntityMapper: (SqlCursor) -> CoinHistoryEntity = { cursor ->
        val columnMap = cursor.columnNames
        CoinHistoryEntity(
            id = cursor.getString(columnMap["id"]?: 0) ?: "",
            type = cursor.getString(columnMap["type"]?:0)?.toTransactionType() ?: TransactionType.EARN,
            amount = cursor.getLong(columnMap["amount"] ?: 0)?.toInt() ?: 0,
            date = cursor.getLong(columnMap["date"] ?: 0) ?: 0L,
            description = cursor.getString(columnMap["description"]?:0) ?: ""
        )
    }

    // Chèn CoinHistoryEntity
    suspend fun insertTransaction(history: CoinHistoryEntity): Long = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                val id = UUID.randomUUID().toString()
                transaction.execute(
                    sql = """
                        INSERT INTO coin_history_table 
                        (id ,type, amount, date, description) 
                        VALUES (? , ?, ?, ?, ?)
                    """.trimIndent(),
                    parameters = listOf(
                        id,
                        history.type.toDbString(),
                        history.amount,
                        history.date,
                        history.description
                    )
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi chèn CoinHistoryEntity: ${e.message}", e)
            }
        }
    }

    // Lấy tất cả CoinHistoryEntity
    suspend fun getAllTransactions(): List<CoinHistoryEntity> = withContext(Dispatchers.IO) {
        try {
            database.getAll(
                sql = "SELECT * FROM coin_history_table",
                mapper = coinHistoryEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy danh sách CoinHistoryEntity: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Theo dõi thay đổi trong coin_history_table theo thời gian thực
    fun watchAllTransactions(): Flow<List<CoinHistoryEntity>> {
        return database.watch(
            sql = "SELECT * FROM coin_history_table ORDER BY date DESC",
            mapper = coinHistoryEntityMapper
        )
    }

    // Theo dõi các bảng thay đổi (coin_history_table)
    fun onTablesChanged(): Flow<Set<String>> {
        return database.onChange(
            tables = setOf("coin_history_table"),
            throttleMs = Queries.DEFAULT_THROTTLE.inWholeMilliseconds,
            triggerImmediately = true
        )
    }

    // Hàm tiện ích: Chuyển đổi TransactionType
    private fun TransactionType.toDbString(): String = when (this) {
        TransactionType.EARN -> "EARN"
        TransactionType.SPEND -> "SPEND"
    }

    private fun String.toTransactionType(): TransactionType = when (this) {
        "EARN" -> TransactionType.EARN
        "SPEND" -> TransactionType.SPEND
        else -> throw IllegalArgumentException("Invalid TransactionType: $this")
    }
}